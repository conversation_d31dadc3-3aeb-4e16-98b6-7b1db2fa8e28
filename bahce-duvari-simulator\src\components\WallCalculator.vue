<template>
  <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
    <!-- Sol Panel - Kontrol Paneli -->
    <div class="xl:col-span-1 space-y-6">
      <DimensionsPanel />
      <RebarPanel />
      <SurfaceTreatmentsPanel />
      <PricingPanel />
    </div>

    <!-- Orta Panel - SVG Görselleştirici -->
    <div class="xl:col-span-1">
      <WallVisualizer />
    </div>

    <!-- Sağ Panel - Sonuçlar ve Rapor -->
    <div class="xl:col-span-1 space-y-6">
      <QuickSummary />
      <TestCalculations />
      <DetailedReport />
    </div>
  </div>
</template>

<script setup lang="ts">
import DimensionsPanel from './panels/DimensionsPanel.vue'
import RebarPanel from './panels/RebarPanel.vue'
import SurfaceTreatmentsPanel from './panels/SurfaceTreatmentsPanel.vue'
import PricingPanel from './panels/PricingPanel.vue'
import WallVisualizer from './WallVisualizer.vue'
import QuickSummary from './QuickSummary.vue'
import TestCalculations from './TestCalculations.vue'
import DetailedReport from './DetailedReport.vue'
</script>
