<template>
  <div class="flex flex-col h-screen">
    <!-- Üst Kontrol <PERSON> -->
    <div class="bg-white border-b border-gray-200 p-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <button
            @click="toggleLayout"
            class="btn-secondary flex items-center space-x-2"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
            <span>{{ isCompactMode ? 'Genişletilmiş Görünüm' : 'Kompakt Görünüm' }}</span>
          </button>

          <button
            @click="toggleFullscreen"
            class="btn-secondary flex items-center space-x-2"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"/>
            </svg>
            <span>Tam Ekran</span>
          </button>

          <div class="flex items-center space-x-2">
            <label class="text-sm font-medium text-gray-700">Görünüm:</label>
            <select v-model="viewMode" class="input-field w-32">
              <option value="section">Kesit</option>
              <option value="3d">3D Görünüm</option>
              <option value="plan">Plan</option>
              <option value="elevation">Cephe</option>
            </select>
          </div>
        </div>

        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-600">Zoom: {{ Math.round(zoomLevel * 100) }}%</span>
          <button @click="resetView" class="btn-secondary text-sm">Sıfırla</button>
        </div>
      </div>
    </div>

    <!-- Ana İçerik Alanı -->
    <div class="flex-1 flex overflow-hidden">
      <!-- Sol Sidebar - Kontrol Panelleri -->
      <div
        :class="[
          'bg-gray-50 border-r border-gray-200 overflow-y-auto transition-all duration-300',
          isCompactMode ? 'w-80' : 'w-96'
        ]"
        v-show="!isFullscreen"
      >
        <div class="p-4 space-y-4">
          <CollapsiblePanel title="📏 Boyutlar" :defaultOpen="true">
            <DimensionsPanel />
          </CollapsiblePanel>

          <CollapsiblePanel title="🔩 Donatı" :defaultOpen="false">
            <RebarPanel />
          </CollapsiblePanel>

          <CollapsiblePanel title="🎨 Yüzey İşlemleri" :defaultOpen="false">
            <SurfaceTreatmentsPanel />
          </CollapsiblePanel>

          <CollapsiblePanel title="💰 Fiyatlar" :defaultOpen="false">
            <PricingPanel />
          </CollapsiblePanel>
        </div>
      </div>

      <!-- Orta Panel - Gelişmiş Görselleştirici -->
      <div class="flex-1 flex flex-col">
        <AdvancedWallVisualizer
          :viewMode="viewMode"
          :isFullscreen="isFullscreen"
          @zoom-change="handleZoomChange"
        />
      </div>

      <!-- Sağ Sidebar - Sonuçlar -->
      <div
        :class="[
          'bg-gray-50 border-l border-gray-200 overflow-y-auto transition-all duration-300',
          isCompactMode ? 'w-80' : 'w-96'
        ]"
        v-show="!isFullscreen"
      >
        <div class="p-4 space-y-4">
          <CollapsiblePanel title="⚡ Hızlı Özet" :defaultOpen="true">
            <QuickSummary />
          </CollapsiblePanel>

          <CollapsiblePanel title="🧪 Test Sonuçları" :defaultOpen="false">
            <TestCalculations />
          </CollapsiblePanel>

          <CollapsiblePanel title="📋 Detaylı Rapor" :defaultOpen="false">
            <DetailedReport />
          </CollapsiblePanel>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DimensionsPanel from './panels/DimensionsPanel.vue'
import RebarPanel from './panels/RebarPanel.vue'
import SurfaceTreatmentsPanel from './panels/SurfaceTreatmentsPanel.vue'
import PricingPanel from './panels/PricingPanel.vue'
import AdvancedWallVisualizer from './AdvancedWallVisualizer.vue'
import QuickSummary from './QuickSummary.vue'
import TestCalculations from './TestCalculations.vue'
import DetailedReport from './DetailedReport.vue'
import CollapsiblePanel from './CollapsiblePanel.vue'

// Layout state
const isCompactMode = ref(false)
const isFullscreen = ref(false)
const viewMode = ref<'section' | '3d' | 'plan' | 'elevation'>('section')
const zoomLevel = ref(1)

const toggleLayout = () => {
  isCompactMode.value = !isCompactMode.value
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  if (isFullscreen.value) {
    document.documentElement.requestFullscreen?.()
  } else {
    document.exitFullscreen?.()
  }
}

const resetView = () => {
  zoomLevel.value = 1
}

const handleZoomChange = (newZoom: number) => {
  zoomLevel.value = newZoom
}
</script>
