<template>
  <div class="flex flex-col h-full bg-white">
    <!-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> -->
    <div class="border-b border-gray-200 p-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <!-- Zoom <PERSON> -->
          <div class="flex items-center space-x-2">
            <button @click="zoomOut" class="p-1 hover:bg-gray-100 rounded">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
              </svg>
            </button>
            <span class="text-sm font-mono w-12 text-center">{{ Math.round(zoom * 100) }}%</span>
            <button @click="zoomIn" class="p-1 hover:bg-gray-100 rounded">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
              </svg>
            </button>
          </div>

          <!-- Görünüm Kontrolleri -->
          <div class="flex items-center space-x-2">
            <button
              @click="fitToScreen"
              class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
            >
              Ekrana Sığdır
            </button>
            <button
              @click="resetView"
              class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
            >
              Sıfırla
            </button>
          </div>
        </div>

        <!-- Görselleştirme Seçenekleri -->
        <div class="flex items-center space-x-4">
          <label class="flex items-center space-x-2">
            <input
              v-model="showRebar"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="text-sm text-gray-700">Donatı</span>
          </label>
          
          <label class="flex items-center space-x-2">
            <input
              v-model="showDimensions"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="text-sm text-gray-700">Ölçüler</span>
          </label>
          
          <label class="flex items-center space-x-2">
            <input
              v-model="showGrid"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="text-sm text-gray-700">Grid</span>
          </label>
        </div>
      </div>
    </div>

    <!-- Ana SVG Görselleştirici -->
    <div 
      ref="containerRef"
      class="flex-1 overflow-hidden relative bg-gray-50"
      @wheel="handleWheel"
      @mousedown="handleMouseDown"
      @mousemove="handleMouseMove"
      @mouseup="handleMouseUp"
      @mouseleave="handleMouseUp"
    >
      <svg
        ref="svgRef"
        :viewBox="viewBox"
        class="w-full h-full cursor-grab"
        :class="{ 'cursor-grabbing': isDragging }"
        style="user-select: none;"
      >
        <!-- Definitions -->
        <defs>
          <!-- Grid Pattern -->
          <pattern 
            id="grid" 
            :width="gridSize * zoom" 
            :height="gridSize * zoom" 
            patternUnits="userSpaceOnUse"
          >
            <path 
              :d="`M ${gridSize * zoom} 0 L 0 0 0 ${gridSize * zoom}`" 
              fill="none" 
              stroke="#e5e7eb" 
              stroke-width="0.5"
            />
          </pattern>
          
          <!-- Fine Grid Pattern -->
          <pattern 
            id="fineGrid" 
            :width="gridSize * zoom / 5" 
            :height="gridSize * zoom / 5" 
            patternUnits="userSpaceOnUse"
          >
            <path 
              :d="`M ${gridSize * zoom / 5} 0 L 0 0 0 ${gridSize * zoom / 5}`" 
              fill="none" 
              stroke="#f3f4f6" 
              stroke-width="0.25"
            />
          </pattern>

          <!-- Gradients -->
          <linearGradient id="wallGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:#f3f4f6;stop-opacity:1" />
            <stop offset="50%" style="stop-color:#e5e7eb;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#d1d5db;stop-opacity:1" />
          </linearGradient>
          
          <linearGradient id="foundationGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#9ca3af;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#6b7280;stop-opacity:1" />
          </linearGradient>
        </defs>

        <!-- Background Grid -->
        <rect 
          v-if="showGrid"
          x="0" 
          y="0" 
          width="100%" 
          height="100%" 
          fill="url(#fineGrid)"
        />
        <rect 
          v-if="showGrid"
          x="0" 
          y="0" 
          width="100%" 
          height="100%" 
          fill="url(#grid)"
        />

        <!-- Main Wall Structure -->
        <g :transform="`translate(${panX}, ${panY}) scale(${zoom})`">
          <!-- Foundation -->
          <rect
            :x="foundationX"
            :y="foundationY"
            :width="foundationWidth"
            :height="foundationHeight"
            fill="url(#foundationGradient)"
            stroke="#4b5563"
            stroke-width="2"
            rx="2"
          />

          <!-- Wall -->
          <rect
            :x="wallX"
            :y="wallY"
            :width="wallWidth"
            :height="wallHeight"
            fill="url(#wallGradient)"
            stroke="#4b5563"
            stroke-width="2"
            rx="1"
          />

          <!-- Surface Treatments -->
          <g v-if="surfaceTreatments.plaster.enabled">
            <!-- Left Plaster -->
            <rect
              :x="wallX - plasterThickness"
              :y="wallY"
              :width="plasterThickness"
              :height="wallHeight"
              fill="#fef3c7"
              stroke="#f59e0b"
              stroke-width="1"
              opacity="0.8"
            />
            <!-- Right Plaster -->
            <rect
              v-if="surfaceTreatments.plaster.sides === 'double'"
              :x="wallX + wallWidth"
              :y="wallY"
              :width="plasterThickness"
              :height="wallHeight"
              fill="#fef3c7"
              stroke="#f59e0b"
              stroke-width="1"
              opacity="0.8"
            />
          </g>

          <!-- Paint Layers -->
          <g v-if="surfaceTreatments.paint.enabled">
            <rect
              :x="wallX - plasterThickness - paintThickness"
              :y="wallY"
              :width="paintThickness"
              :height="wallHeight"
              fill="#ddd6fe"
              stroke="#8b5cf6"
              stroke-width="0.5"
              opacity="0.6"
            />
            <rect
              v-if="surfaceTreatments.paint.sides === 'double'"
              :x="wallX + wallWidth + plasterThickness"
              :y="wallY"
              :width="paintThickness"
              :height="wallHeight"
              fill="#ddd6fe"
              stroke="#8b5cf6"
              stroke-width="0.5"
              opacity="0.6"
            />
          </g>

          <!-- Coping -->
          <rect
            v-if="surfaceTreatments.coping"
            :x="copingX"
            :y="copingY"
            :width="copingWidth"
            :height="copingHeight"
            fill="#fbbf24"
            stroke="#f59e0b"
            stroke-width="2"
            rx="1"
          />

          <!-- Rebar System -->
          <g v-if="showRebar">
            <!-- Foundation Longitudinal Rebar -->
            <g v-for="(rebar, index) in foundationLongitudinalRebars" :key="`long-${index}`">
              <circle
                :cx="rebar.x"
                :cy="rebar.y"
                :r="getRebarRadius(rebarDetails.foundationLongitudinal.diameter)"
                fill="#8b5cf6"
                stroke="#7c3aed"
                stroke-width="1"
              />
            </g>

            <!-- Foundation Stirrups -->
            <g v-for="(stirrup, index) in foundationStirrups" :key="`stirrup-${index}`">
              <rect
                :x="stirrup.x"
                :y="stirrup.y"
                :width="stirrup.width"
                :height="stirrup.height"
                fill="none"
                stroke="#ef4444"
                :stroke-width="getRebarStrokeWidth(rebarDetails.foundationStirrup.diameter)"
                rx="2"
              />
            </g>

            <!-- Wall Vertical Rebar -->
            <g v-for="(rebar, index) in wallVerticalRebars" :key="`vert-${index}`">
              <line
                :x1="rebar.x"
                :y1="rebar.y1"
                :x2="rebar.x"
                :y2="rebar.y2"
                stroke="#10b981"
                :stroke-width="getRebarStrokeWidth(rebarDetails.wallVertical.diameter)"
                stroke-linecap="round"
              />
            </g>

            <!-- Wall Horizontal Rebar -->
            <g v-for="(rebar, index) in wallHorizontalRebars" :key="`horiz-${index}`">
              <line
                :x1="rebar.x1"
                :y1="rebar.y"
                :x2="rebar.x2"
                :y2="rebar.y"
                stroke="#f59e0b"
                :stroke-width="getRebarStrokeWidth(rebarDetails.wallHorizontal.diameter)"
                stroke-linecap="round"
              />
            </g>
          </g>

          <!-- Dimensions -->
          <g v-if="showDimensions">
            <!-- Foundation Width -->
            <DimensionLine
              :x1="foundationX"
              :y1="foundationY + foundationHeight + 40"
              :x2="foundationX + foundationWidth"
              :y2="foundationY + foundationHeight + 40"
              :label="`${dimensions.foundationWidth} cm`"
              :zoom="zoom"
            />

            <!-- Foundation Depth -->
            <DimensionLine
              :x1="foundationX - 40"
              :y1="foundationY"
              :x2="foundationX - 40"
              :y2="foundationY + foundationHeight"
              :label="`${dimensions.foundationDepth} cm`"
              :zoom="zoom"
              vertical
            />

            <!-- Wall Height -->
            <DimensionLine
              :x1="wallX + wallWidth + 40"
              :y1="wallY"
              :x2="wallX + wallWidth + 40"
              :y2="wallY + wallHeight"
              :label="`${dimensions.height} m`"
              :zoom="zoom"
              vertical
            />

            <!-- Wall Thickness -->
            <DimensionLine
              :x1="wallX"
              :y1="wallY - 40"
              :x2="wallX + wallWidth"
              :y2="wallY - 40"
              :label="`${dimensions.thickness} cm`"
              :zoom="zoom"
            />
          </g>
        </g>

        <!-- Legend -->
        <g class="legend" transform="translate(20, 20)">
          <rect 
            x="0" 
            y="0" 
            width="220" 
            height="140" 
            fill="white" 
            stroke="#d1d5db" 
            stroke-width="1" 
            rx="6"
            opacity="0.95"
          />
          <text x="10" y="20" class="text-sm font-medium" fill="#374151">Malzeme Gösterimi</text>
          
          <g transform="translate(10, 35)">
            <rect x="0" y="0" width="15" height="10" fill="url(#wallGradient)" stroke="#4b5563"/>
            <text x="20" y="8" class="text-xs" fill="#6b7280">Beton Duvar</text>
          </g>
          
          <g transform="translate(10, 50)">
            <rect x="0" y="0" width="15" height="10" fill="url(#foundationGradient)" stroke="#4b5563"/>
            <text x="20" y="8" class="text-xs" fill="#6b7280">Temel</text>
          </g>
          
          <g v-if="showRebar" transform="translate(10, 65)">
            <line x1="0" y1="5" x2="15" y2="5" stroke="#10b981" stroke-width="3"/>
            <text x="20" y="8" class="text-xs" fill="#6b7280">Dikey Donatı</text>
          </g>
          
          <g v-if="showRebar" transform="translate(10, 80)">
            <line x1="0" y1="5" x2="15" y2="5" stroke="#f59e0b" stroke-width="3"/>
            <text x="20" y="8" class="text-xs" fill="#6b7280">Yatay Donatı</text>
          </g>
          
          <g v-if="surfaceTreatments.plaster.enabled" transform="translate(10, 95)">
            <rect x="0" y="0" width="15" height="10" fill="#fef3c7" stroke="#f59e0b"/>
            <text x="20" y="8" class="text-xs" fill="#6b7280">Sıva</text>
          </g>
          
          <g v-if="surfaceTreatments.paint.enabled" transform="translate(10, 110)">
            <rect x="0" y="0" width="15" height="10" fill="#ddd6fe" stroke="#8b5cf6"/>
            <text x="20" y="8" class="text-xs" fill="#6b7280">Boya</text>
          </g>
        </g>

        <!-- Zoom Info -->
        <g class="zoom-info" transform="translate(20, 400)">
          <rect 
            x="0" 
            y="0" 
            width="120" 
            height="60" 
            fill="white" 
            stroke="#d1d5db" 
            stroke-width="1" 
            rx="4"
            opacity="0.9"
          />
          <text x="10" y="20" class="text-xs font-medium" fill="#374151">Görünüm Bilgisi</text>
          <text x="10" y="35" class="text-xs" fill="#6b7280">Zoom: {{ Math.round(zoom * 100) }}%</text>
          <text x="10" y="50" class="text-xs" fill="#6b7280">Ölçek: 1:{{ Math.round(100 / zoom) }}</text>
        </g>
      </svg>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { storeToRefs } from 'pinia'
import { useCalculationStore } from '../stores/calculation'
import DimensionLine from './DimensionLine.vue'

interface Props {
  viewMode: 'section' | '3d' | 'plan' | 'elevation'
  isFullscreen: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  zoomChange: [zoom: number]
}>()

const calculationStore = useCalculationStore()
const { dimensions, rebarDetails, surfaceTreatments } = storeToRefs(calculationStore)

// Refs
const containerRef = ref<HTMLElement>()
const svgRef = ref<SVGElement>()

// State
const zoom = ref(1)
const panX = ref(0)
const panY = ref(0)
const isDragging = ref(false)
const lastMousePos = ref({ x: 0, y: 0 })
const showRebar = ref(true)
const showDimensions = ref(true)
const showGrid = ref(true)

// Constants
const baseScale = 3 // pixels per cm
const gridSize = 20
const minZoom = 0.1
const maxZoom = 5

// Computed properties for dynamic sizing
const containerSize = ref({ width: 800, height: 600 })

const actualScale = computed(() => baseScale * zoom.value)

// Calculate optimal dimensions based on wall size
const wallBounds = computed(() => {
  const maxDim = Math.max(
    dimensions.value.foundationWidth,
    dimensions.value.foundationDepth,
    dimensions.value.thickness,
    dimensions.value.height * 100
  )

  const padding = 200 // Extra space for dimensions and labels
  const totalWidth = Math.max(800, maxDim * actualScale.value + padding * 2)
  const totalHeight = Math.max(600, (dimensions.value.height * 100 + dimensions.value.foundationDepth) * actualScale.value + padding * 2)

  return { width: totalWidth, height: totalHeight }
})

const viewBox = computed(() => {
  const bounds = wallBounds.value
  return `0 0 ${bounds.width} ${bounds.height}`
})

// Wall geometry calculations with dynamic scaling
const foundationWidth = computed(() => dimensions.value.foundationWidth * actualScale.value)
const foundationHeight = computed(() => dimensions.value.foundationDepth * actualScale.value)
const foundationX = computed(() => (wallBounds.value.width - foundationWidth.value) / 2)
const foundationY = computed(() => wallBounds.value.height - foundationHeight.value - 100)

const wallWidth = computed(() => dimensions.value.thickness * actualScale.value)
const wallHeight = computed(() => dimensions.value.height * 100 * actualScale.value)
const wallX = computed(() => foundationX.value + (foundationWidth.value - wallWidth.value) / 2)
const wallY = computed(() => foundationY.value - wallHeight.value)

const copingWidth = computed(() => (dimensions.value.thickness + 10) * actualScale.value)
const copingHeight = computed(() => 10 * actualScale.value)
const copingX = computed(() => wallX.value - 5 * actualScale.value)
const copingY = computed(() => wallY.value - copingHeight.value)

const plasterThickness = computed(() => 2 * actualScale.value)
const paintThickness = computed(() => 1 * actualScale.value)

// Rebar calculations with dynamic scaling
const foundationLongitudinalRebars = computed(() => {
  const rebars = []
  const count = rebarDetails.value.foundationLongitudinal.count
  const spacing = foundationWidth.value / (count + 1)
  const cover = rebarDetails.value.concreteCover * actualScale.value

  for (let i = 1; i <= count; i++) {
    rebars.push({
      x: foundationX.value + i * spacing,
      y: foundationY.value + foundationHeight.value - cover
    })
  }
  return rebars
})

const foundationStirrups = computed(() => {
  const stirrups = []
  const spacing = rebarDetails.value.foundationStirrup.spacing * actualScale.value
  const cover = rebarDetails.value.concreteCover * actualScale.value
  const count = Math.floor(foundationWidth.value / spacing)

  for (let i = 0; i < count; i++) {
    stirrups.push({
      x: foundationX.value + cover + i * spacing,
      y: foundationY.value + cover,
      width: foundationWidth.value - 2 * cover,
      height: foundationHeight.value - 2 * cover
    })
  }
  return stirrups
})

const wallVerticalRebars = computed(() => {
  const rebars = []
  const spacing = rebarDetails.value.wallVertical.spacing * actualScale.value
  const cover = rebarDetails.value.concreteCover * actualScale.value
  const count = Math.floor(wallWidth.value / spacing)
  const anchorage = rebarDetails.value.anchorageLength * actualScale.value

  for (let i = 0; i <= count; i++) {
    rebars.push({
      x: wallX.value + cover + i * spacing,
      y1: foundationY.value + foundationHeight.value - anchorage,
      y2: wallY.value + cover
    })
  }
  return rebars
})

const wallHorizontalRebars = computed(() => {
  const rebars = []
  const spacing = rebarDetails.value.wallHorizontal.spacing * actualScale.value
  const cover = rebarDetails.value.concreteCover * actualScale.value
  const count = Math.floor(wallHeight.value / spacing)

  for (let i = 0; i <= count; i++) {
    rebars.push({
      x1: wallX.value + cover,
      x2: wallX.value + wallWidth.value - cover,
      y: wallY.value + wallHeight.value - cover - i * spacing
    })
  }
  return rebars
})

// Helper functions
const getRebarRadius = (diameter: number) => {
  return Math.max(2, diameter * actualScale.value / 10)
}

const getRebarStrokeWidth = (diameter: number) => {
  return Math.max(1, diameter * actualScale.value / 20)
}

// Zoom and pan functions
const zoomIn = () => {
  const newZoom = Math.min(maxZoom, zoom.value * 1.2)
  setZoom(newZoom)
}

const zoomOut = () => {
  const newZoom = Math.max(minZoom, zoom.value / 1.2)
  setZoom(newZoom)
}

const setZoom = (newZoom: number) => {
  zoom.value = newZoom
  emit('zoomChange', newZoom)
}

const fitToScreen = () => {
  if (!containerRef.value) return

  const container = containerRef.value.getBoundingClientRect()
  const wallBoundsValue = wallBounds.value

  const scaleX = container.width / wallBoundsValue.width
  const scaleY = container.height / wallBoundsValue.height
  const optimalZoom = Math.min(scaleX, scaleY) * 0.9 // 90% to leave some margin

  setZoom(Math.max(minZoom, Math.min(maxZoom, optimalZoom)))
  panX.value = 0
  panY.value = 0
}

const resetView = () => {
  setZoom(1)
  panX.value = 0
  panY.value = 0
}

// Mouse event handlers
const handleWheel = (event: WheelEvent) => {
  event.preventDefault()

  const delta = event.deltaY > 0 ? 0.9 : 1.1
  const newZoom = Math.max(minZoom, Math.min(maxZoom, zoom.value * delta))
  setZoom(newZoom)
}

const handleMouseDown = (event: MouseEvent) => {
  isDragging.value = true
  lastMousePos.value = { x: event.clientX, y: event.clientY }
}

const handleMouseMove = (event: MouseEvent) => {
  if (!isDragging.value) return

  const deltaX = event.clientX - lastMousePos.value.x
  const deltaY = event.clientY - lastMousePos.value.y

  panX.value += deltaX
  panY.value += deltaY

  lastMousePos.value = { x: event.clientX, y: event.clientY }
}

const handleMouseUp = () => {
  isDragging.value = false
}

// Resize observer
const updateContainerSize = () => {
  if (containerRef.value) {
    const rect = containerRef.value.getBoundingClientRect()
    containerSize.value = { width: rect.width, height: rect.height }
  }
}

let resizeObserver: ResizeObserver | null = null

onMounted(() => {
  updateContainerSize()

  if (containerRef.value) {
    resizeObserver = new ResizeObserver(updateContainerSize)
    resizeObserver.observe(containerRef.value)
  }

  // Auto-fit on mount
  nextTick(() => {
    fitToScreen()
  })
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})

// Watch for dimension changes and auto-fit
watch(
  () => [dimensions.value.length, dimensions.value.height, dimensions.value.thickness, dimensions.value.foundationWidth, dimensions.value.foundationDepth],
  () => {
    nextTick(() => {
      fitToScreen()
    })
  },
  { deep: true }
)
</script>
